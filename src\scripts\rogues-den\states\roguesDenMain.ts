import { createState, State } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Dialogue } from '../../../api/game/dialogue'
import { Equipment } from '../../../api/game/equipment'
import { GameObjects } from '../../../api/game/gameObjects'
import { GeAction } from '../../../api/game/geAction'
import { GroundItems } from '../../../api/game/groundItems'
import { Inventory } from '../../../api/game/inventory'
import { Npcs } from '../../../api/game/npcs'
import { Skill } from '../../../api/game/skill'
import { Walking } from '../../../api/game/walking'
import { Widgets } from '../../../api/game/widgets'
import { WorldHopping } from '../../../api/game/worldHopping'
import { MenuOpcode } from '../../../api/model/menuOpcode'
import { Tile } from '../../../api/model/tile'
import { TradePackage } from '../../../api/model/tradePackage'
import { ResupplyMuleState } from '../../../api/script-utils/mule/resupplyMuleStrategy'
import { DefaultGeActionsState } from '../../../api/script-utils/states/geStates'
import { PaintTimer } from '../../../api/utils/paintTimer'
import { Time } from '../../../api/utils/time'
import { Client } from '../../../api/wrappers/client'
import { Player } from '../../../api/wrappers/player'
import { ItemPredicate } from '../../../data/itemPredicates'
import { MuleReceiver } from '../../muling/muleReceiver'
import { ItemId } from '../../../data/itemId'
import { Teleport } from '../../../data/teleport'
import { log } from '../../../api/utils/utils'
import { ItemContainer } from '../../../api/model/itemContainer'
import { RogueEquipment } from '../rogueEquipment'
import { AccountProp, AccountProps } from '../../../api/model/accountData'

export class RoguesDenMain extends State {
   
    public static completions = 0
    public static attempts = 0
    private timer = new PaintTimer()
    private needToGetPermission = false


     public static get lastStep() {
        return AccountProps.get('roguesDen.lastStep') ?? 0
    }

    public static set lastStep(value: number) {
        AccountProps.set('roguesDen.lastStep', value)
    }

    public static get currentStep() {
        return AccountProps.get('roguesDen.currentStep') ?? 0
    }

    public static set currentStep(value: number) {
        AccountProps.set('roguesDen.currentStep', value)
    }

    TILE_GRACE = new Tile(3049, 4963, 1)

    resupply = new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), 308)

    geState = new DefaultGeActionsState(() => this, this.resupply, [
        GeAction.item(ItemId.GAMES_NECKLACE8, 8).gePrice(1.1, 1000).predicate(ItemPredicate.gamesNecklace),
        GeAction.item(ItemId.JUG_OF_WINE, 800).gePrice(1.1, 50),
        GeAction.item(ItemId.STAMINA_POTION4, 20).gePrice(1.1, 1000),
    ])

    openCrate = createState('Opening rogue chest crate', () => {
        if (!Bank.hasCache()) {
            Bank.openNearest()
            return
        }
        if (Bank.cache.contains(ItemId.ROGUES_EQUIPMENT_CRATE) && !Inventory.contains(ItemId.ROGUES_EQUIPMENT_CRATE)) {
            Withdraw.id(ItemId.ROGUES_EQUIPMENT_CRATE, 1).orState(this.geState).withdraw()
            return
        }

        if (!Inventory.contains(ItemId.ROGUES_EQUIPMENT_CRATE)) {
            this.setDefaultState()
            return
        }

        if (Bank.isOpen()) {
            log('Closing bank')
            Widgets.closeTopInterface()
        }

        if (!Dialogue.isOpen()) {
            Inventory.get().byId(ItemId.ROGUES_EQUIPMENT_CRATE).click(57, 2)
            Time.sleepCycles(3)
            return
        }

        let nextPiece = 'none'

        if (!ItemContainer.allCombined().contains(RogueEquipment.GLOVES.id)) {
            nextPiece = 'Gloves'
        }
        if (!ItemContainer.allCombined().contains(RogueEquipment.BOOTS.id)) {
            nextPiece = 'Boots'
        }

        if (!ItemContainer.allCombined().contains(RogueEquipment.MASK.id)) {
            nextPiece = 'Mask'
        }

        if (!ItemContainer.allCombined().contains(RogueEquipment.BODY.id)) {
            nextPiece = 'Top'
        }
        if (!ItemContainer.allCombined().contains(RogueEquipment.LEGS.id)) {
            nextPiece = 'Trousers'
        }

        Dialogue.goNext('A piece of rogue equipment', nextPiece)
    })

    drinkStamina = createState('Drink stamina before trip', () => {
        if (Player.getRunEnergy() > 80 && Player.isStaminaPotionActive()) {
            this.setDefaultState()
            return
        }

        if (!Withdraw.predicate(ItemPredicate.staminaPotion, 1).orState(this.geState).withdraw()) return false

        if (Bank.isOpen()) {
            Inventory.get(Inventory.bank).getByPredicate(ItemPredicate.staminaPotion)?.click(1007, 9)
        } else {
            Inventory.getByPredicate(ItemPredicate.staminaPotion)?.click(57, 2)
        }

        Time.sleep(400, 1000)
    })

    onGameMessage(username: string, message: string): void {
        if (message.includes('as you enter the maze')) {
            RoguesDenMain.attempts++
        }
        if (message.includes('start cracking the safe')) {
            RoguesDenMain.completions++
        }
    }

    onBackgroundAction(): void {
        if (Skill.HITPOINTS.getPercent() <= 70 && Inventory.getById(ItemId.JUG_OF_WINE)) {
            Inventory.getById(ItemId.JUG_OF_WINE).click(57, 2)
            Time.sleep(400, 1000)
            return
        }

        Walking.setRunAuto()
    }

    onAction() {
        Walking.setRunAuto()

        if (AccountProps.getOrDefault('account.roguesOutfit', 'false') == 'true') {
            this.startNextScript()
            return
        }

        if (ItemContainer.allCombined().containsAllById(RogueEquipment.GLOVES.id, RogueEquipment.BOOTS.id, RogueEquipment.MASK.id, RogueEquipment.LEGS.id, RogueEquipment.BODY.id)) {
            log('Contains')
            AccountProps.set('account.roguesOutfit', 'true')
            this.startNextScript()
            return
        } else {
            log('Not contain')
            AccountProps.set('account.roguesOutfit', 'false')
        }

        if (!this.isAtMinigame()) {
            RoguesDenMain.currentStep = 0
            this.timer.reset()
        }
        if (!WorldHopping.switchToP2pExcept()) {
            return
        }
        if (new Tile(3061, 4984, 1).distance() <= 4) {
            if (GameObjects.getById(7259) != null) {
                GameObjects.getById(7259).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                Time.sleep(1000, 2000)
                return
            }
        }

        if (!this.goToRoguesDen()) {
            return
        }
        if (!this.doFailsafes()) {
            return
        }

        switch (RoguesDenMain.currentStep) {
            case 0:
                this.step0()
                break
            case 1:
                this.step1()
                break
            case 2:
                this.step2()
                break
            case 3:
                this.step3()
                break
            // case 4: step4();
            case 5:
                this.step5()
                break
            case 6:
                this.step6()
                break
            case 7:
                this.step7()
                break
            case 8:
                this.step8()
                break
            case 9:
                this.step9()
                break
            case 10:
                this.step10()
                break
            case 11:
                this.step11()
                break
            case 12:
                this.step12()
                break
            case 13:
                this.step13()
                break
            case 14:
                this.step14()
                break
            case 15:
                this.step15()
                break
            case 16:
                this.step16()
                break
            case 17:
                this.step17()
                break
            case 18:
                this.step18()
                break
            case 19:
                this.step19()
                break
            case 20:
                this.step20()
                break
            case 21:
                this.step21()
                break
            case 22:
                this.step22()
                break
            case 23:
                this.step23()
                break
            case 24:
                this.step24()
                break
                case 24.1:
                this.step24_1()
                break
            case 25:
                this.step25()
                break
            case 25.1:
                this.step25_1()
                break
            case 26:
                this.step26()
                break
            case 27:
                this.step27()
                break
            case 28:
                this.step28()
                break
            case 29:
                this.step29()
                break
            case 30:
                this.step30()
                break
            case 31:
                this.step31()
                break
            case 32:
                this.step32()
                break
            case 33:
                this.step33()
                break
            case 34:
                this.step34()
                break
        }
    }

    public isAtRogueDen() {
        return this.TILE_GRACE.distance() < 80 || this.isAtMinigame()
    }

    private goToRoguesDen() {
        if (this.isAtRogueDen()) {
            return true
        }
        if (!this.ensureItems()) {
            return false
        }

        if (new Tile(2895, 3534, 0).distance() > 80 || this.TILE_GRACE.distance() > 80) {
            return Walking.walkTo(this.TILE_GRACE, 5)
        }
    }

    public isAtMinigame() {
        return Inventory.get().contains(ItemId.MYSTIC_JEWEL)
    }

    private doFailsafes() {
        if (RoguesDenMain.currentStep != RoguesDenMain.lastStep) {
            RoguesDenMain.lastStep = RoguesDenMain.currentStep
            this.timer.reset()
        }
        if (RoguesDenMain.currentStep == 0 && this.isAtMinigame() && !new Tile(3050, 4997, 1).isReachable()) {
            //Logged in but state not recognized.
            this.resetMinigame()
            return false
        }
        if (RoguesDenMain.currentStep > 0 && this.timer.getElapsedSeconds() > 100) {
            //Other fail - restart
            this.resetMinigame()
            return false
        }
        return true
    }

    public resetMinigame() {
        if (Dialogue.isOpen()) {
            Dialogue.goNext("Yes I'd like to")
        } else {
            Inventory.get().byId(ItemId.MYSTIC_JEWEL).click(57, 2) //TODO
            Time.sleep(500, 1500)
        }
    }

    private step0() {
        if (new Tile(3050, 4997, 1).isReachable()) {
            RoguesDenMain.currentStep = 1
            return
        }

        if (Dialogue.contains('And where do you think')) {
            this.needToGetPermission = true
        }

        if (Dialogue.contains('Nothing thanks')) {
            this.needToGetPermission = false
        }

        if (this.needToGetPermission) {
            if (Dialogue.talkTo(Npcs.getByNameEquals("Brian O'Richard"))) {
                Dialogue.goNext('Yes actually', 'Ok that sounds good', 'Nothing thanks')
            }
            if (Dialogue.contains('Great!')) {
                this.needToGetPermission = false
            }
            return
        }

        if (Dialogue.contains("Sorry but I can't allow you")) {
            Npcs.getByNameEquals('Rocky').click(MenuOpcode.NPC_THIRD_OPTION)
            Time.sleep(3000)
            return
        }

        if (Equipment.isEquippedByPredicate((i) => i.id != -1)) {
            if (Bank.openNearest()) {
                Bank.depositAll()
                Bank.depositEquipment()
                Time.sleep(600, 1500)
                return
            }
        }

        if (Player.getRunEnergy() < 80 || !Player.isStaminaPotionActive()) {
            this.setState(this.drinkStamina)
            return
        }

        if (Inventory.contains(ItemId.ROGUES_EQUIPMENT_CRATE) || Bank.cache?.contains(ItemId.ROGUES_EQUIPMENT_CRATE)) {
            this.setState(this.openCrate)
            return
        }
        6

        //enter
        if (Inventory.getFreeSlots() < 28) {
            Bank.openNearest() && Bank.depositAll()
        } else {
            if (Walking.clickTile(new Tile(3056, 4991, 1), 10)) {
                GameObjects.getById(7256).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                Time.sleep(() => new Tile(3050, 4997, 1).isReachable())
            }
        }
    }

    private step1() {
        const tile = new Tile(3048, 4997, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 2
            return
        }
        GameObjects.getById(7251).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
        Time.sleep(() => tile.distance() == 0)
    }

    private step2() {
        const tile = new Tile(3037, 5002, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 3
            return
        }
        Walking.clickTile(new Tile(3037, 5002, 1), 0)
        Time.sleep(4000, () => tile.distance() == 0)
    }

    private step3() {
        const tile = new Tile(3023, 5001, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 5
            return
        }
        GameObjects.getById(7255, new Tile(3023, 5001, 1)).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
        Time.sleep(() => tile.distance() == 0)
    }

    // private step4() {
    //     if(new Tile(3023, 5001, 1).distance() == 0) {
    //         currentStep = 5;
    //         return;
    //     }
    // }

    private step5() {
        const tile = new Tile(3014, 5003, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 6
            return
        }

        Walking.clickTile(new Tile(3014, 5003, 1), 0)
        Time.sleep(() => tile.distance() == 0)
    }
    private step6() {
        const tile = new Tile(3013, 5004, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 7
            return
        }
        Walking.clickTile(new Tile(3013, 5004, 1), 0)
        Time.sleep(() => tile.distance() == 0)
    }
    private step7() {
        const tile = new Tile(3011, 5005, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 8
            return
        }
        Walking.clickTile(new Tile(3011, 5005, 1), 0)
        Time.sleep(2500, () => tile.distance() == 0)
    }
    private step8() {
        const tile = new Tile(2988, 5005, 1)
        if (tile.distance() <= 2) {
            Time.sleep(3000)
            RoguesDenMain.currentStep = 9
            return
        }

        if (Player.local?.isMoving) {
            Time.sleep(3000)
            return
        }

        if (Player.local?.isAnimating) {
            Time.sleep(3000)
            return
        }

        GameObjects.getById(7240, new Tile(2994, 5004, 1))?.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)

        if (Time.sleep(() => Player.local?.isAnimating == true)) {
            Time.sleep(8000)
        }

        Time.sleep(3000)

        if (Time.sleep(10000, () => tile.distance() <= 2)) {
            Time.sleep(3000)
        }
    }

    private step9() {
        const tile = new Tile(2967, 5017, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 10
            return
        }
        Walking.clickTile(new Tile(2969, 5017, 1))
        Time.sleep(2500, () => tile.distance() == 0)
    }

    private step10() {
        const tile = new Tile(2958, 5035, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 11
            return
        }
        // walk to new Tile(2969, 5017, 1)
        GameObjects.getById(7239, new Tile(2969, 5017, 1)).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
        Time.sleep(5000, () => tile.distance() == 0)
    }

    private step11() {
        const tile = new Tile(2964, 5051, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 12
            return
        }
        if (Walking.clickTile(new Tile(2964, 5051, 1), 0)) {
            GameObjects.getById(7227).click(MenuOpcode.GAME_OBJECT_FIFTH_OPTION)
            Time.sleep(() => tile.distance() == 0)
        }
    }

    private step12() {
        const tile = new Tile(2957, 5072, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 13
            return
        }
        if (Walking.clickTile(new Tile(2957, 5068, 1), 0)) {
            GameObjects.getById(7219, new Tile(2957, 5068, 1)).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
            Time.sleep(() => tile.distance() == 0)
        }
    }

    private step13() {
        const tile = new Tile(2957, 5076, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 14
            return
        }
        Walking.clickTile(tile)
        Time.sleep(() => tile.distance() == 0)
    }
    private step14() {
        const tile = new Tile(2955, 5098, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 15
            return
        }
        if (Walking.clickTile(new Tile(2955, 5094, 1))) {
            GameObjects.getById(7219, new Tile(2955, 5094, 1), 3).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
            Time.sleep(() => tile.distance() == 0)
        }
    }

    private step15() {
        const tile = new Tile(2972, 5094, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 16
            return
        }
        if (Walking.clickTile(new Tile(2972, 5098, 1), 0)) {
            GameObjects.getById(7219, new Tile(2972, 5098, 1)).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
            Time.sleep(() => tile.distance() <= 1)
        }
    }

    private step16() {
        const tile = new Tile(2972, 5093, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 17
            return
        }
        GameObjects.getById(7255, new Tile(2972, 5098, 1)).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
        Time.sleep(() => tile.distance() == 0)
    }
    private step17() {
        const tile = new Tile(2976, 5086, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 18
            return
        }
        Walking.clickTile(tile)
        Time.sleep(() => tile.distance() == 0)
    }
    private step18() {
        const tile = new Tile(2991, 5087, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 19
            return
        }
        if (Walking.clickTile(new Tile(2982, 5087, 1))) {
            GameObjects.getById(7240).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
            Time.sleep(() => tile.distance() == 0)
            return
        }
    }

    private step19() {
        if (Walking.clickTile(new Tile(2992, 5088, 1))) {
            GameObjects.getById(7249).click(1001)
            if (Time.waitForGameMessage('disarm the trap')) {
                Time.sleepCycles(3)
                RoguesDenMain.currentStep = 20
            }
        }
    }

    private step20() {
        const tile = new Tile(2999, 5088, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 21
            return
        }
        if (Walking.clickTile(new Tile(2999, 5088, 1))) {
            Time.sleepCycles(3)
        }
    }

    private step21() {
        const tile = new Tile(3006, 5088, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 22
            return
        }
        if (Walking.clickTile(tile)) {
            Time.sleep(() => tile.distance() == 0)
        }
    }

    private step22() {
        //picking the tile
        const tile = new Tile(3024, 5082, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 23
            return
        }
        if (Inventory.get().contains(ItemId.TILE_5568)) {
            const w = Widgets.get(45088773)
            if (w != null && w.isRendered) {
                w.click(57, 1)
                Time.sleep(1000, 2000)
            } else {
                GameObjects.getById(7234).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                Time.sleep(() => Widgets.get(45088773) != null)
                Time.sleep(200, 600)
            }
        } else {
            GroundItems.getById(5568).click(MenuOpcode.GROUND_ITEM_THIRD_OPTION)
            Time.sleepCycles(3)
        }
    }

    private step23() {
        const tile = new Tile(3038, 5065, 1)

        const doorTiles = [
            new Tile(3030, 5079, 1),
            new Tile(3032, 5078, 1),
            new Tile(3036, 5076, 1),
            new Tile(3039, 5079, 1),
            new Tile(3042, 5076, 1),
            new Tile(3044, 5069, 1),
            new Tile(3041, 5068, 1),
            new Tile(3040, 5070, 1),
            new Tile(3038, 5069, 1),
        ]

        if (tile.isReachable() && tile.distance() < 5) {
            RoguesDenMain.currentStep = 24
            return
        }

        for (const doorTile of doorTiles) {
            log(doorTile + ' reachable: ' + doorTile.isReachable())
            if (doorTile.isReachable()) {
                GameObjects.getById(7255, doorTile).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
                Time.sleep(1000, 2000)
                return
            }
        }
        //WALKING SHIT
        //Time.sleep(() -> tile.distance() == 0);
    }

    private step24() {

     

        if (Walking.clickTile(new Tile(3028, 5034, 1), 0)) {
           RoguesDenMain.currentStep = 24.1
           //GameObjects.getById(7249).click(1001)
           //if (Time.waitForGameMessage('disarm the trap')) {
           //    RoguesDenMain.currentStep = 25
           //}
        }
    }

     private step24_1() {
          if (Walking.clickTile(new Tile(3024, 5034, 1), 0)) {
           RoguesDenMain.currentStep = 25
          }
     }
   
    private step25() {
        const tile = new Tile(3014, 5033, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 25.1
            return
        }
        if (Walking.clickTile(new Tile(3015, 5033, 1))) {
            GameObjects.getById(7255, new Tile(3015, 5033, 1)).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
            Time.sleep(() => tile.distance() == 0)
        }
    }

    private step25_1() {
        if (Walking.getRunEnergy() < 5) {
            log('Waiting for run energy')
            return
        }

        const tile = new Tile(3010, 5033, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 26
            return
        }
        Walking.clickTile(new Tile(3010, 5033, 1))

        //    GameObjects.getById(7230, new Tile(3014, 5033, 1)).click(1001)
        //    if(Time.waitForGameMessage("disarm the trap")) {
        //         Time.sleepCycles(3)
        //    }
    }

    private step26() {
        if (new Tile(3010, 5033, 1).distance() == 0) {
            GameObjects.getById(7255, new Tile(3010, 5033, 1)).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
            return
        }

        const tile = new Tile(3007, 5033, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 27
            return
        }

        Walking.clickTile(new Tile(3008, 5033, 1), 0)

        if (Walking.clickTile(new Tile(3008, 5033, 1), 0)) {
            GameObjects.getById(7230, new Tile(3008, 5033, 1)).click(1001)
            if (Time.waitForGameMessage('disarm the trap')) {
                RoguesDenMain.currentStep = 27
            }
        }
    }

    private step27() {
        const tile = new Tile(2999, 5034, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 28
            return
        }
        if (Walking.clickTile(tile, 0)) {
            Time.sleep(() => tile.distance() == 0)
        }
    }

    private step28() {
        const tile = new Tile(2992, 5045, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 29
            return
        }
        if (Walking.clickTile(tile, 0)) {
            Time.sleep(() => tile.distance() == 0)
        }
    }

    private step29() {
        const tile = new Tile(2992, 5051, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 30
            return
        }
        if (Walking.clickTile(tile, 0)) {
            Time.sleep(() => tile.distance() == 0)
        }
    }

    private step30() {
        const tile = new Tile(2992, 5067, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 31
            return
        }
        if (Walking.clickTile(tile, 0)) {
            Time.sleep(() => tile.distance() == 0)
        }
    }

    private step31() {
        if (!Inventory.get().contains(ItemId.FLASH_POWDER)) {
            if (Walking.clickTile(new Tile(3009, 5063, 1), 1)) {
                GroundItems.getById(ItemId.FLASH_POWDER).click(MenuOpcode.GROUND_ITEM_THIRD_OPTION)
                Time.sleepCycles(3)
            }
        } else {
            const guard = Npcs.getById(3191)
            Inventory.get().byId(ItemId.FLASH_POWDER).useOnNpc(guard)
            if (Time.sleep(() => Inventory.get().getCount(ItemId.FLASH_POWDER) <= 4)) {
                RoguesDenMain.currentStep = 32
            }
        }
    }
    private step32() {
        const tile = new Tile(3030, 5055, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 33
            return
        }
        if (Walking.clickTile(tile, 0)) {
            Time.sleep(1000, () => tile.distance() == 0)
        }
    }

    private step33() {
        const tile = new Tile(3024, 5046, 1)
        if (tile.distance() == 0) {
            RoguesDenMain.currentStep = 34
            return
        }
        if (Walking.clickTile(tile, 0)) {
            Time.sleep(2000, () => tile.distance() == 0)
        }
    }
    private step34() {
        const tile = new Tile(3015, 5045, 1)
        if (Walking.clickTile(tile, 4)) {
            GameObjects.getById(7237)?.click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
            Time.sleep(5000)
        }
    }

    private ensureItems() {
        if (!Withdraw.predicate(ItemPredicate.gamesNecklace).amount(1).ensureSpace().orState(this.geState).withdraw()) return false
        return true
    }
}

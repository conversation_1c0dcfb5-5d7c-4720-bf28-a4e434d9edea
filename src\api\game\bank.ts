import { Bot } from '../../core/bot'
import { ItemDefinitions } from '../../data/itemDefinitions'
import { Locations } from '../../data/locations'
import { Input } from '../core/input/input'
import { State } from '../core/script/state'
import { StatefulScript } from '../core/script/statefulScript'
import { Item } from '../model/item'
import { ItemContainer } from '../model/itemContainer'
import { Tile } from '../model/tile'
import { distinctBy } from '../utils/arrayExtensions'
import { BehavioralSleep } from '../utils/behavior-fingerprinting/behavioralSleep'
import { deleteCache, putCache } from '../utils/cache'
import { Time } from '../utils/time'
import { log } from '../utils/utils'
import { Client } from '../wrappers/client'
import { Game } from '../wrappers/game'
import { GameObjects } from './gameObjects'
import { InputBox } from './inputBox'
import { Inventory } from './inventory'
import { Varps } from './varps'
import { Walking } from './walking'
import { Widgets } from './widgets'

type StateConstructor<T extends State> = new () => T

export class Bank {
    // static WITHDRAW_MODE_X = 140;
    // static WITHDRAW_MODE_ALL = 144;
    // static WITHDRAW_MODE_1 = 128;
    // static WITHDRAW_MODE_5 = 132;
    // static WITHDRAW_MODE_10 = 136;

    static WITHDRAW_MODE_X = 12
    static WITHDRAW_MODE_ALL = 16
    static WITHDRAW_MODE_1 = 0
    static WITHDRAW_MODE_5 = 4
    static WITHDRAW_MODE_10 = 8

    static BANK_CHESTS = [26711, 4483, 26707, 10562, 28595, 44630, 28594, 54933]
    static BANK_IDS = [10060, 10583, 27291, 10355, 24101, 25808, 10517, 52397, ...this.BANK_CHESTS]

    static _cache: ItemContainer | null
    static get cache(): ItemContainer | null {
        this.isOpen()
        if (this._cache != null) return this._cache
    }

    static hasCache() { return this.cache != null}

    static get(): ItemContainer {
        const root = Widgets.getByRootId(12, 13)?.children
        const arr: Array<Item> = []

        if (root == null) return new ItemContainer([])

        for (var i = 0; i < root.getChildrenCount(); i++) {
            var child = root?.getAt(i)

            if (child == null) continue
            if (child.itemId == 6512) continue
            if (child.itemAmount <= 0) continue
            if (child.isHidden) continue

            var item = new Item(child.itemId, child.itemAmount, i, 786445, child)
            arr.push(item)
        }

        const container = new ItemContainer(arr)
        return container
    }

    static contains(itemId: number, amount: number = 1) {
        return this.get().contains(itemId, amount)
    }

    static containsByPredicate(itemPredicate: (item: Item) => boolean) {
        return this.get().containsByPredicate(itemPredicate)
    }

    static byId(itemId: number): Item {
        return this.get().byId(itemId)
    }

    static getByPredicate(itemPredicate: (i: Item) => boolean) {
        return this.get().getByPredicate(itemPredicate)
    }

    static isOpen(): boolean {
        const open = Widgets.get(786476)?.isRendered == true

        if (open) {
            this._cache = this.get()
            deleteCache('bankCache')

            const closeWidget1 = Widgets.getPackedChild(********, 0)
            if (closeWidget1 != null) {
                closeWidget1.click(57, 1)
                Time.sleep(2000)
                return false
            }
        }

        return open
    }

    public static get isNoted(): boolean {
        return (Game.getConfig(115) & 1) == 1
    }

    public static get withdrawAmount(): number {
        return Game.getConfig(304) / 2
    }

    static open(sleep: boolean = true, clickEvenIfOpen: boolean = false): boolean {
        log('Opening bank')
        if (Bank.isOpen() && !clickEvenIfOpen) return true

        const obj = GameObjects.get((o) => Bank.BANK_IDS.findIndex((id) => id == o.id) > -1)

        log('Found: ', obj != null)
        if (obj == null) return false

        if (Bank.BANK_CHESTS.find((c) => c == obj.id)) {
            obj.click(3)
        } else {
            obj.click(4)
        }

        if(!sleep) return true
        
        if(Time.sleep(() => this.isOpen())) {
            BehavioralSleep.onInterfaceAppeared()
            return true
        }
        return false
    }

    static openNearest(nearestTo?: Tile, clickEvenIfOpen: boolean = false): boolean {
        
        //Game is loading or something, prevent banking to not pick incorrect bank (at this state positions are broken etc.)
        if(Client.gameState != 30){
            return false
        }

        if (Bank.isOpen()) {
            //  if (!this.skipBankHints()) return false;
            if (!this.ensureItemsInBank()) return false
            return true
        }

        const closestBank = Locations.getClosestBank(nearestTo)
        try {
            log('Opening bank at ', closestBank)
            if (!Walking.walkTo(closestBank, 12)) {
                log('r1')
                return false
            }
        } catch (e) {
            log('Error in openNearest: ', e)
            for (let i = 0; i < 15; i++) {
                if (Walking.canFindPath(closestBank, true)) return false
                Time.sleep(1000)
            }
            log("Bank Error 1: " + e)
            return false
        }

        if (!closestBank.isReachable()) {
            Walking.walkTo(closestBank, 0)
            return
        }

        return this.open()
    }

    static depositEquipment() {
        Widgets.get(786478)?.click(57, 1)
        return true
    }

    static depositAll(sleep: boolean = true): boolean {
        Bank.handleCoinPouch()
        Widgets.get(786476)?.click(57, 1)
        return sleep ? Time.sleep(() => Inventory.getFreeSlots() == 28) : true
    }

    static handleCoinPouch() {
        const pouch = Inventory.get().getAny(22521, 22531)
        if (pouch == null) return
        pouch.click(57, 2)
    }

    static ensureItemsInBank(): boolean {
        if (Bank.get().countByPredicate((i) => true) == 0) {
            Bank.depositAll()
            Time.sleep(600, 1500)
            return false
        }

        return true
    }

    static deposit(item: Item, sleep: boolean = true): void {
        log('Deposit: ', JSON.stringify(item))
        Bank.handleCoinPouch()
        item.click(1007, 8)
        if (sleep) Time.sleep(600)
    }

    static depositAllExcept(ids: number[]): boolean {
        let toDeposit = Inventory.get(Inventory.bank).items.filter((item) => !ids.includes(item.id))
        toDeposit = distinctBy(toDeposit, (i) => i.id)

        toDeposit.forEach((item) => Bank.deposit(item))
        return Time.sleep(() => !Inventory.get(Inventory.bank).items.some((item) => !ids.includes(item.id)))
    }

    static depositAllExceptPredicate(predicate: (item: Item) => boolean): boolean {
        let toDeposit = Inventory.get(Inventory.bank).items.filter((item) => !predicate(item))
        toDeposit = distinctBy(toDeposit, (i) => i.id)
        toDeposit.forEach((item) => Bank.deposit(item))
        return Time.sleep(() => !Inventory.get(Inventory.bank).items.some((item) => !predicate(item)))
    }

    static depositAllByPredicate(predicate: (item: Item) => boolean): void {
        let toDeposit = Inventory.get(Inventory.bank).where(predicate)
        toDeposit = distinctBy(toDeposit, (key) => key.id)
        toDeposit.forEach((item) => Bank.deposit(item))
        Time.sleep(() => Inventory.get(Inventory.bank).countByPredicate(predicate) <= 0)
    }

    static openAndWithdraw(request: Withdraw): boolean {
        if (!request.isWithdrawAll && !request.withdrawExactAmount && Inventory.get().countByPredicate(request._itemPredicateAtInventory) >= request.withdrawMinimumAmount) {
            return true
        }

        if (
            request.isWithdrawAll &&
            !request.withdrawExactAmount &&
            Bank.hasCache() &&
            !Bank.cache.containsByPredicate(request._itemPredicateAtBank) &&
            Inventory.get().countByPredicate(request._itemPredicateAtInventory) >= request.withdrawMinimumAmount
        ) {
            return true
        }

        if (request.withdrawExactAmount && Inventory.get().countByPredicate(request._itemPredicateAtInventory) == request.amountToWithdraw) {
            log('Exact amount 1')
            return true
        }

        if (request.withdrawExactAmount && Inventory.get().countByPredicate(request._itemPredicateAtInventory) > request.amountToWithdraw) {
            log('Exact amount - We got more than we need')
            Bank.openNearest() && Bank.depositAllByPredicate(request._itemPredicateAtInventory)
            return false
        }

        const itemDef = [...ItemDefinitions.definitions.values()].find((def) => request._itemPredicateAtInventory(new Item(def.id)))

        log(
            'We have amount of this item: ',
            itemDef?.name,
            (Bank.cache?.countByPredicate(request._itemPredicateAtBank) ?? 0) + Inventory.get().countByPredicate(request._itemPredicateAtInventory),
            'But amountToWithdraw: ',
            request.amountToWithdraw
        )

        if (!Bank.openNearest()) {
            return false
        }

        if(request.isDepositNotedItems && Inventory.get().hasThisItemNoted(request._itemPredicateAtInventory)){
            Bank.depositAll()
            return false
        }

        Bank.withdraw(request)

        if (request.withdrawExactAmount) {
            log('Checking for exact amount')
            return Inventory.get().countByPredicate(request._itemPredicateAtInventory) == request.amountToWithdraw
        }

        return Inventory.get().countByPredicate(request._itemPredicateAtInventory) >= request.withdrawMinimumAmount
    }

    static openAndWithdrawSilent(id: number, amount: number, noted: boolean = false, ensureSpace: boolean = false): boolean {
        const req = new Withdraw((i) => i.id == id, amount, false, ensureSpace, noted)
        return Bank.openAndWithdraw(req)
    }

    static openAndWithdrawSilentRequest(req: Withdraw): boolean {
        try {
            return Bank.openAndWithdraw(req)
        } catch (e) {
            if (e.name == 'BankItemNotFoundException') return false
            throw e
        }
    }

    static setNoted(noted: boolean): boolean {
        log(`Noted: ${this.isNoted}`)
        if (this.isNoted == noted) return true

        if (noted) {
            Widgets.get(786458).click(57, 1)
        } else {
            Widgets.get(786456).click(57, 1)
        }

        return Time.sleep(() => this.isNoted == noted)
    }

    static clickWithdraw(item: Item, amount: number, noted: boolean = false, ensureSpace: boolean = false, sleep: boolean = false, exactAmount: boolean = false, withdrawAll: boolean = false): void {
        const primaryAmount = amount
        log("clickWithdraw 1")
        if (item == null) return
        log("clickWithdraw 2")
        
        if (!this.setNoted(noted)) return
        log("clickWithdraw 3")

        if (Varps.withdrawAmountMode != this.WITHDRAW_MODE_1) {
            log('Varps.withdrawAmountMode != this.WITHDRAW_MODE_1', Varps.withdrawAmountMode)
            Widgets.get(786462)?.click(57, 1)
            Time.sleep(() => Varps.withdrawAmountMode == this.WITHDRAW_MODE_1)
        }

        if (exactAmount) {
            log('Exact amount. Left to withdraw: ' + amount)
            if (!Inventory.hasSpace(noted ? item.id + 1 : item.id, amount)) {
                this.depositAll()
                return
            }

            if (amount < 0) {
                this.deposit(Inventory.get(Inventory.bank).byId(item.id))
                if (!Time.sleep(() => Inventory.get(Inventory.bank).countByIds(item.id) == 0)) {
                    return
                }
                amount = primaryAmount
            }

            if (amount == 0) {
                return
            }
        }

        if (ensureSpace) {
            log('Ensure space')
            let itemId = item.id
            let itemAmount = Math.max(0, Math.min(amount, Inventory.get(Inventory.bank).and(Bank.get()).byId(item.id)?.amount ?? 0))
            if (noted && !item.definition.isStackable) itemId = item.definition.realNotedId

            if (!Inventory.hasSpace(itemId, itemAmount)) {
                this.depositAll()
                if (!Time.sleep(() => Inventory.getFreeSlots() >= 28)) {
                    return
                }
            }
        }

        log('Withdraw: ', amount)
        const hasLastX = this.withdrawAmount != 0

        switch (amount) {
            case 1:
                // item.click(1007, Varps.withdrawAmount !== 1 && Varps.withdrawAmountMode !== this.WITHDRAW_MODE_1 ? 2 : 1);
                item.click(57, 1)
                return
            case 5:
                item.click(57, 2)
                return
            case 10:
                item.click(57, 3)
                return
            case 28:
                item.click(57, hasLastX ? 6 : 5)
                return
        }

        if (this.withdrawAmount == amount) {
            item?.click(57, 4)
            return
        }

        if (withdrawAll) {
            item?.click(57, hasLastX ? 6 : 5)
            return
        }

        log('Withdraw X: ', amount, " hasLastX: ", hasLastX, " this.withdrawAmount", this.withdrawAmount)
        item?.click(57, hasLastX ? 5 : 4)

        if (Time.sleep(() => InputBox.isOpen)) {
            Input.type('' + amount)
            Time.sleep(100, 300)
            Input.enter()
        }
    }

    static withdraw(request: Withdraw): void {


        function onNotFound() {
            if (request._notFoundState != null) {
                const state = request._notFoundState()
                ;(Bot.scriptHandler.currentScript as StatefulScript).currentState = state
                throw new BankActionInterruptedException(request)
            } else {
                throw new BankItemNotFoundException(request)
            }
        }

        let container = Bank.get()
        if (request.isOrderByItemName) {
            container = container.orderItemsByName(false)
        }

        if (request.isOrderByItemNameAscending) {
            container = container.orderItemsByName(true)
        }

        // Find all matching items in the bank
        const matchingItems = container.where(request._itemPredicateAtBank)

        // Calculate the total amount of matching items
        let totalAmount = 0
        for (const item of matchingItems) {
            totalAmount += item.amount
        }

        totalAmount += Inventory.get().countByPredicate(request._itemPredicateAtInventory)

        log("Total amount we have: ", totalAmount)

        if (totalAmount < request.withdrawMinimumAmount) {
            const itemDef = [...ItemDefinitions.definitions.values()].find((def) => request._itemPredicateAtInventory(new Item(def.id)))
            log('Not found state triggered by: ', itemDef?.name, ' | Total amount in bank: ', totalAmount, 'Min amount: ', request.amountToWithdraw)
            onNotFound()
            return
        }

        if (request.withdrawExactAmount) {
            request.amountToWithdraw = Math.max(request.amountToWithdraw - Inventory.get().countByPredicate(request._itemPredicateAtInventory), 0)
            log("request.amountToWithdraw ", request.amountToWithdraw)
        }

        // Withdraw the first matching item (or noted version if requested)
        const firstMatchingItem = matchingItems[0]

        if(request.amountToWithdraw > 0 && !firstMatchingItem) {
            onNotFound()
            return
        }

        Bank.clickWithdraw(firstMatchingItem, request.amountToWithdraw, request.isNoted, request.isEnsureSpace, request.isSleep, request.withdrawExactAmount, request.isWithdrawAll)

        // If withdrawAll is true and noted is requested, withdraw the rest of the matching items
        if (request.isWithdrawAll && request.isNoted) {
            for (let i = 1; i < matchingItems.length; i++) {
                Bank.clickWithdraw(matchingItems[i], matchingItems[i].amount, request.isNoted, request.isEnsureSpace, request.isSleep, false, true)
            }
        }
    }

    static withdrawEnsureSpace(id: number, amount: number): void {
        Bank.openAndWithdraw(new Withdraw((i) => i.id == id, amount, false, true, false))
    }
}

export class Withdraw {
    public _itemPredicateAtInventory: (item: Item) => boolean
    public _itemPredicateAtBank: (item: Item) => boolean

    public _notFoundState?: () => State
    public amountToWithdraw?: number
    public isWithdrawAll?: boolean
    public isEnsureSpace?: boolean = true
    public isNoted?: boolean
    public isSleep?: boolean = true
    // public isSleepForAmount?: number = 1;
    public withdrawExactAmount: boolean = false
    public withdrawMinimumAmount?: number = 1
    public isOrderByItemName?: boolean = false
    public isOrderByItemNameAscending?: boolean = false
    public isDepositNotedItems?: boolean = false

    constructor(predicate?: (item: Item) => boolean, amount?: number, withdrawAll?: boolean, ensureSpace?: boolean, noted?: boolean) {
        this._itemPredicateAtInventory = predicate
        this.amountToWithdraw = amount
        this.isWithdrawAll = withdrawAll
        this.isNoted = noted
        this.isEnsureSpace = ensureSpace  ?? true
    }

    static builder(): Withdraw {
        return new Withdraw()
    }

    static id(itemId: number, amount?: number, minimumAmount?: number): Withdraw {
        const req = new Withdraw()

        if (minimumAmount != null) req.minimumAmount(minimumAmount)

        req._itemPredicateAtBank = (item) => item.id == itemId
        req._itemPredicateAtInventory = (item) => (!req.isNoted && item.id == itemId) || (req.isNoted && item.id == itemId + 1)

        if (amount != null) req.amountToWithdraw = amount

        return req
    }

    static predicate(predicate: (item: Item) => boolean, amount?: number, minimumAmount?: number): Withdraw {
        const req = new Withdraw()
        if (minimumAmount != null) req.minimumAmount(minimumAmount)

        req._itemPredicateAtBank = predicate
        req._itemPredicateAtInventory = (item) => (!req.isNoted && predicate(item)) || (req.isNoted && predicate(item) && item?.definition?.isNoted)

        if (amount != null) req.amountToWithdraw = amount
        return req
    }

    public sleep(sleep: boolean = true): Withdraw {
        this.isSleep = sleep
        return this
    }

    public id(itemId: number): Withdraw {
        this._itemPredicateAtBank = (item) => item.id == itemId
        this._itemPredicateAtInventory = (item) => (!this.isNoted && item.id == itemId) || (this.isNoted && item.id == itemId + 1)
        return this
    }

    public predicate(predicate: (item: Item) => boolean): Withdraw {
        this._itemPredicateAtBank = predicate
        this._itemPredicateAtInventory = (item) => (!this.isNoted && predicate(item)) || (this.isNoted && predicate(item) && item.definition?.isNoted)
        return this
    }

    public orState(notFoundState: () => State): Withdraw
    public orState(state: State): Withdraw
    public orState<T extends State>(cls: StateConstructor<T>): Withdraw



    public orState(arg: (() => State) | StateConstructor<State> | State): Withdraw {
        if (typeof arg === 'function') {
            if (arg.prototype instanceof State) {
                // Handle case where `arg` is a class constructor
                this._notFoundState = () => new (arg as StateConstructor<State>)()
            } else {
                // Handle case where `arg` is a function returning a State
                this._notFoundState = arg as () => State
            }
        } else {
            // Handle case where `arg` is an instance of State
            this._notFoundState = () => arg as State
        }
        return this
    }

        public depositNotedItems(depositNotedItems: boolean = true): Withdraw {
        this.isDepositNotedItems = depositNotedItems
        return this
    }

    public orderByName(): Withdraw {
        this.isOrderByItemName = true
        return this
    }
    public orderByNameAscending(): Withdraw {
        this.isOrderByItemNameAscending = true
        return this
    }

    public amount(amount: number, isAlsoMinimum?: boolean): Withdraw {
        this.amountToWithdraw = amount
        if (isAlsoMinimum == true) this.minimumAmount(amount)
        return this
    }

    public exactAmount(exact: boolean = true): Withdraw {
        this.withdrawExactAmount = exact
        return this
    }

    public ensureSpace(space: boolean = true): Withdraw {
        this.isEnsureSpace = space
        return this
    }
    public minimumAmount(withdrawMinimumAmount: number = 1): Withdraw {
        this.withdrawMinimumAmount = withdrawMinimumAmount
        return this
    }

    public noted(isNoted: boolean = true): Withdraw {
        this.isNoted = isNoted
        return this
    }

    public withdrawAll(isWithdrawAll: boolean = true): Withdraw {
        this.isWithdrawAll = isWithdrawAll
        return this
    }

    public static process(...withdrawRequests: Withdraw[]): boolean {
        for (const req of withdrawRequests) {
            if (!req.withdraw()) {
                return false
            }
        }

        return true
    }

    static inventory(notFoundState: State, ...withdrawRequests: Withdraw[]) {
        Bank.depositAllExceptPredicate((item) => withdrawRequests.find((r) => r._itemPredicateAtInventory?.(item)) != null)

        return this.all(notFoundState, ...withdrawRequests)
    }

    public static all(notFoundState: State, ...withdrawRequests: Withdraw[]): boolean {
        for (const req of withdrawRequests) {
            req.orState(() => notFoundState)
            if (!req.withdraw()) {
                return false
            }
        }

        return true
    }

    public withdraw(): boolean {
        Bank.openAndWithdraw(this)
        return this.doSleep()
    }

    public withdrawSilently(): boolean {
        Bank.openAndWithdrawSilentRequest(this)
        return this.doSleep()
    }

    private doSleep(): boolean {
        if (this.isWithdrawAll && this.isNoted) {
            return Time.sleep(() => Bank.get().countByPredicate(this._itemPredicateAtBank) == 0)
        }
        if (this.withdrawExactAmount) {
            return Time.sleep(() => Inventory.get().countByPredicate(this._itemPredicateAtInventory) == this.amountToWithdraw)
        }
        if (this.withdrawMinimumAmount) {
            return Time.sleep(() => Inventory.get().countByPredicate(this._itemPredicateAtInventory) >= this.withdrawMinimumAmount)
        }

        return false
    }
}

export class BankItemNotFoundException extends Error {
    public withdrawRequest?: Withdraw

    constructor(withdrawRequest: Withdraw) {
        super('BankItemNotFoundException')
        this.withdrawRequest = withdrawRequest
        this.name = 'BankItemNotFoundException'
    }
}

export class BankActionInterruptedException extends Error {
    public withdrawRequest?: Withdraw

    constructor(withdrawRequest: Withdraw) {
        super('BankItemNotFoundException')
        this.withdrawRequest = withdrawRequest
        this.name = 'BankActionInterruptedException'
    }
}

import { ItemId } from "../../../data/itemId";
import { ItemCombination } from "../../../scripts/herblore/itemCombination";
import { EqItem, Equipment } from "../../game/equipment";
import { Skill } from "../../game/skill";
import { Varps } from "../../game/varps";
import { ItemContainer } from "../../model/itemContainer";
import { obtainThievingCapeState } from "./obtainItemStates";

export enum EqSetTag {
    SKILLING = "skilling"
}

export const eqSets = {

    masterFarmerWithCape: {
        tags: [EqSetTag.SKILLING],
        enabled: () => Varps.membershipDays > 0 && Skill.THIEVING.getCurrentLevel() >= 99, 
        priority: 2,
        items: [
            EqItem.id(ItemId.ROGUE_MASK).slot(Equipment.SLOT_HELM),
            EqItem.id(ItemId.ROGUE_TROUSERS).slot(Equipment.SLOT_LEGS),
            EqItem.id(ItemId.ROGUE_TOP).slot(Equipment.SLOT_CHEST),
            EqItem.id(ItemId.ROGUE_GLOVES).slot(Equipment.SLOT_GLOVES),
            EqItem.id(ItemId.ROGUE_BOOTS).slot(Equipment.SLOT_BOOTS),
            EqItem.id(ItemId.THIEVING_CAPE).slot(Equipment.SLOT_CAPE).obtainState(() => obtainThievingCapeState),
        ]
    },

    graceful: {
        tags: [EqSetTag.SKILLING],
        enabled: () => Varps.membershipDays > 0
            &&  ItemContainer.allCombined().containsAllById(ItemId.GRACEFUL_HOOD, ItemId.GRACEFUL_LEGS, ItemId.GRACEFUL_TOP, ItemId.GRACEFUL_CAPE, ItemId.GRACEFUL_GLOVES, ItemId.GRACEFUL_BOOTS),
        priority: 1,
        items: [
            EqItem.id(ItemId.GRACEFUL_HOOD).slot(Equipment.SLOT_HELM),
            EqItem.id(ItemId.GRACEFUL_LEGS).slot(Equipment.SLOT_LEGS),
            EqItem.id(ItemId.GRACEFUL_TOP).slot(Equipment.SLOT_CHEST),
            EqItem.id(ItemId.GRACEFUL_CAPE).slot(Equipment.SLOT_CAPE),
            EqItem.id(ItemId.GRACEFUL_GLOVES).slot(Equipment.SLOT_GLOVES),
            EqItem.id(ItemId.GRACEFUL_BOOTS).slot(Equipment.SLOT_BOOTS),
        ]
    },
    set1: {
        tags: [EqSetTag.SKILLING],
         enabled: () => Varps.membershipDays > 0,
        priority: 0,
        items: [
            EqItem.id(ItemId.RED_HALLOWEEN_MASK).slot(Equipment.SLOT_HELM),
            EqItem.id(ItemId.RED_ELEGANT_LEGS).slot(Equipment.SLOT_LEGS),
            EqItem.id(ItemId.RED_ELEGANT_SHIRT).slot(Equipment.SLOT_CHEST),
            EqItem.id(ItemId.RED_FLOWERS).slot(Equipment.SLOT_AMULET),
            EqItem.id(ItemId.RED_BOOTS).slot(Equipment.SLOT_BOOTS),
            EqItem.id(ItemId.TEAM49_CAPE).slot(Equipment.SLOT_CAPE),
            EqItem.id(ItemId.RED_GLOVES).slot(Equipment.SLOT_GLOVES),
        ]
    },
    set2: {
        tags: [EqSetTag.SKILLING],
        enabled: () => Varps.membershipDays > 0,
        priority: 0,
        items: [
            EqItem.id(ItemId.BLUE_HALLOWEEN_MASK).slot(Equipment.SLOT_HELM),
            EqItem.id(ItemId.BLUE_ELEGANT_LEGS).slot(Equipment.SLOT_LEGS),
            EqItem.id(ItemId.BLUE_ELEGANT_SHIRT).slot(Equipment.SLOT_CHEST),
            EqItem.id(ItemId.BLUE_FLOWERS).slot(Equipment.SLOT_AMULET),
            EqItem.id(ItemId.BLUE_BOOTS).slot(Equipment.SLOT_BOOTS),
            EqItem.id(ItemId.TEAM29_CAPE).slot(Equipment.SLOT_CAPE),
            EqItem.id(ItemId.GREY_GLOVES).slot(Equipment.SLOT_GLOVES),
        ]
    },
    set3: {
        tags: [EqSetTag.SKILLING],
        enabled: () => Varps.membershipDays > 0,
        priority: 0,
        items: [
            EqItem.id(ItemId.GREEN_HALLOWEEN_MASK).slot(Equipment.SLOT_HELM),
            EqItem.id(ItemId.GREEN_ELEGANT_LEGS).slot(Equipment.SLOT_LEGS),
            EqItem.id(ItemId.GREEN_ELEGANT_SHIRT).slot(Equipment.SLOT_CHEST),
            EqItem.id(ItemId.WHITE_FLOWERS).slot(Equipment.SLOT_AMULET),
            EqItem.id(ItemId.GREEN_BOOTS).slot(Equipment.SLOT_BOOTS),
            EqItem.id(ItemId.TEAM39_CAPE).slot(Equipment.SLOT_CAPE),
            EqItem.id(ItemId.FREMENNIK_GLOVES).slot(Equipment.SLOT_GLOVES),
        ]
    },

}
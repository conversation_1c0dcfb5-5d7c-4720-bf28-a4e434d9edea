import { StatefulScript } from '../../api/core/script/statefulScript'
import { Skill } from '../../api/game/skill'
import { AccountData } from '../../api/model/accountData'
import { Item } from '../../api/model/item'
import { TradePackage } from '../../api/model/tradePackage'
import { GiveToMuleState } from '../../api/script-utils/mule/giveMuleStrategy'
import { ResupplyMuleState } from '../../api/script-utils/mule/resupplyMuleStrategy'
import { BehavioralSleep } from '../../api/utils/behavior-fingerprinting/behavioralSleep'
import { formatTime, hourRatio } from '../../api/utils/utils'
import { BotSettings } from '../../botSettings'
import { MuleReceiver } from '../muling/muleReceiver'
import { MainMasterFarmer } from './states/mainMasterFarmer'

export class MasterFarmer extends StatefulScript {
    recentLevel: number

  
    constructor() {
        super()
        this.loopInterval = 200
    }

    onStart(): void {
        this.initWithState(new MainMasterFarmer())
    }

    onDraw(canvas: any, paint: any): void {
        this.drawText('Master Farmer [' + this.currentState.name + ']')
        this.drawText('Runtime: ' + this.progressTracker.getTimeRunning())
        this.drawText('Level: ' + Skill.THIEVING.getCurrentLevel())
        this.drawText(this.progressTracker.getExpGainedString(Skill.THIEVING))
        this.drawText('Actions: ' + this.getActionsDone() + '(' + hourRatio(this.progressTracker.startTime, this.getActionsDone()) + ')')
        if (BehavioralSleep.sleepingUntil != -1) {
            this.drawText(`Sleep: ${BehavioralSleep.sleepReason} (${formatTime(BehavioralSleep.sleepingUntil - Date.now())})`)
        }
    }

    getActionsDone() {
        this.recentLevel = Skill.THIEVING.getCurrentLevel()
        return Math.floor(this.getExpGained() / 43) // Master farmer gives ~43 XP per pickpocket
    }

    getExpGained() {
        return this.progressTracker.getExpGained(Skill.THIEVING)
    }

    runtimeInfo(): string {
        return `<th>Level: ${Skill.THIEVING.getCurrentLevel()}</th><th>${this.progressTracker.getExpGainedString(Skill.THIEVING)}</th><th>Actions: ${this.getActionsDone() + '(' + hourRatio(this.progressTracker.startTime, this.getActionsDone()) + ')'}</th> <th>${this.currentState?.name}</th>`
    }
}
